package co.com.gedsys.aware.ports.inbound;

import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

public interface NotificationApi {
    @GetMapping("")
    ResponseEntity<Page<Notification>> getNotifications(
            @RequestHeader("X-UserName") String username,
            @RequestParam(required = false) NotificationStatus status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size);
    @PatchMapping("/{id}/readed")
    ResponseEntity<Void> markAsRead(@PathVariable UUID id);

}
