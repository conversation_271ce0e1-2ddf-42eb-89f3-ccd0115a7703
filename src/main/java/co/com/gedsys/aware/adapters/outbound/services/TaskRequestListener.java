package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.tasks.TaskEvent;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class TaskRequestListener extends AbstractRabbitMQListener<TaskEvent> {
    @Autowired
    private NotificationServices notificationServices;
    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;

    protected TaskRequestListener(RabbitTemplate rabbitTemplate) {
        super(rabbitTemplate);
    }

    @RabbitListener(queues = {QueueName.TAREAS_ACTUALIZADAS})
    @Override
    public void processMessage(@Payload TaskEvent payload, Message message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    @Override
    protected void handleMessageProcessing(TaskEvent messageReceived) throws IOException {
        try {
            switch (messageReceived.type()) {
                case TaskEvent.Type.ASIGNADA:
                    processTaskEvent(messageReceived, "Tarea asignada", NotificationType.success);
                    break;
                case TaskEvent.Type.VENCIDA:
                    processTaskEvent(messageReceived, "Tarea vencida", NotificationType.warning);
                    break;
                case TaskEvent.Type.PROXIMA:
                    processTaskEvent(messageReceived, "Tarea próxima a vencer", NotificationType.info);
                    break;
                default:
                    processTaskEvent(messageReceived, "Tarea", NotificationType.info);
            }
        } catch (Exception e) {
            System.err.println("Error processing messageReceived: " + e.getMessage());
        }
    }

    private void processTaskEvent(TaskEvent message,String title,NotificationType type){
        // Procesar el mensaje
        Action action = new Action(
                ActionType.link,
                message.taskFormKey() + "/" + message.taskId(),
                "Ver tarea"
        );
        Notification notification = new Notification();
        notification.setOwner(message.assigned());
        notification.setTitle(title);
        notification.setType(type);
        notification.setAction(action);
        notification.setStatus(NotificationStatus.unread);
        notification.setTimestamp(message.timestamp());
        notification.setDetails(message);
        String userName = message.assigned();

        Notification notificationSaved=notificationServices.saveNotification(notification);
        simpMessagingTemplate.convertAndSend("/queue/" + userName + "/tasks", notificationSaved);
    }
}
