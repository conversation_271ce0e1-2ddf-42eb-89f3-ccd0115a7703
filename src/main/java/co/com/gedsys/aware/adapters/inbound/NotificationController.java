package co.com.gedsys.aware.adapters.inbound;

import co.com.gedsys.aware.adapters.outbound.services.NotificationServices;
import co.com.gedsys.aware.ports.inbound.NotificationApi;
import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RequestMapping("/api/v1/notifications")
@RestController
public class NotificationController implements NotificationApi {
    private final NotificationServices notificationServices;

    public NotificationController(NotificationServices notificationServices
                                  ) {
        this.notificationServices = notificationServices;
    }
    @Override
    public ResponseEntity<Page<Notification>> getNotifications(
            @RequestHeader("X-UserName") String username,
            @RequestParam(required = false) NotificationStatus status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        Pageable pageable = PageRequest.of(page, size);
        Page<Notification> notifications = notificationServices.findNotificationsWithFilters(username.trim(), status, startDate, endDate, pageable);
        return ResponseEntity.ok(notifications);
    }

    @Override
    public ResponseEntity<Void> markAsRead(@PathVariable UUID id) {
        System.out.println("id: " + id);
        try {
            notificationServices.markAsRead(id);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
