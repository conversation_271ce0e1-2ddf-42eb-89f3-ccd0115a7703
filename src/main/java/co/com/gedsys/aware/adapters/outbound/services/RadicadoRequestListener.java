// package co.com.gedsys.aware.adapters.outbound.services;

// import co.com.gedsys.aware.ports.models.*;
// import co.com.gedsys.commons.constant.amqp.RadicacionDocumentosQueue;
// import co.com.gedsys.commons.events.documentos.FinProcesoRadicacionEvent;
// import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;
// import com.rabbitmq.client.Channel;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.amqp.core.Message;
// import org.springframework.amqp.rabbit.annotation.RabbitListener;
// import org.springframework.amqp.rabbit.core.RabbitTemplate;
// import org.springframework.amqp.support.AmqpHeaders;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.messaging.handler.annotation.Header;
// import org.springframework.messaging.handler.annotation.Payload;
// import org.springframework.messaging.simp.SimpMessagingTemplate;
// import org.springframework.stereotype.Service;

// import java.io.IOException;
// import java.time.LocalDateTime;
// import java.util.Date;

// @Slf4j
// @Service
// public class RadicadoRequestListener extends AbstractRabbitMQListener<FinProcesoRadicacionEvent> {
//     @Autowired
//     private NotificationServices notificationServices;
//     @Autowired
//     private SimpMessagingTemplate simpMessagingTemplate;
//     protected RadicadoRequestListener(RabbitTemplate rabbitTemplate) {
//         super(rabbitTemplate);
//     }
//     @RabbitListener(queues = RadicacionDocumentosQueue.RADICADOS_COMPLETADOS)
//     @Override
//     public void processMessage(@Payload FinProcesoRadicacionEvent payload, Message message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
//         super.processMessage(payload, message, channel, deliveryTag);
//     }
//     @Override
//     protected void handleMessageProcessing(FinProcesoRadicacionEvent messageReceived) throws IOException {
//         processRadicadoEvent(messageReceived);
//     }
//     private void processRadicadoEvent(FinProcesoRadicacionEvent message){
//         Notification notification = new Notification();
//         notification.setTitle("Radicado completado");
//         notification.setDetails(message);
//         notification.setType(NotificationType.success);
//         notification.setStatus(NotificationStatus.unread);
//         notification.setTimestamp(LocalDateTime.now());
//         notification.setAction(new Action(
//                 ActionType.link,
//                 message.fileId() ,
//                 "Ver Documento"
//         ));
//         message.stakeholders().stream().forEach(stakeholder -> {
//             notification.setOwner(stakeholder);
//             Notification notificationSaved=notificationServices.saveNotification(notification);
//             System.out.println("Radicado completado: " + notificationSaved);
//             simpMessagingTemplate.convertAndSend("/queue/" + stakeholder + "/documento-radicado", notificationSaved);
//         });
//     }

// }
