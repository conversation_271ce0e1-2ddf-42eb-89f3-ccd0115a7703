package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.process.DocumentaryLoadCompleted;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;

@Slf4j
@Service
public class DocumentaryLoadCompleteListener extends AbstractRabbitMQListener<DocumentaryLoadCompleted> {
    @Autowired
    private NotificationServices notificationServices;
    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;

    protected DocumentaryLoadCompleteListener(RabbitTemplate rabbitTemplate) {
        super(rabbitTemplate);
    }

    @RabbitListener(queues = QueueName.DOCUMENTARY_LOAD_COMPLETED, containerFactory = "manualListenerContainerFactory")
    @Override
    public void processMessage(@Payload DocumentaryLoadCompleted payload, Message message, Channel channel,
                               @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    @Override
    protected void handleMessageProcessing(DocumentaryLoadCompleted message) {
        Notification notification = new Notification();
        notification.setTitle(message.description());
        var details = message.properties() != null
                ? message.properties().entrySet().stream()
                .collect(LinkedHashMap::new,
                        (map, entry) -> map.put(entry.getKey(), entry.getValue()),
                        LinkedHashMap::putAll)
                : new LinkedHashMap<>();
        notification.setDetails(details);
        notification.setType(NotificationType.success);
        notification.setStatus(NotificationStatus.unread);
        notification.setTimestamp(LocalDateTime.now());
        notification.setAction(new Action(
                ActionType.link,
                message.documentId(),
                ActionText.VER_DOCUMENTO));
        message.stakeholders().forEach(stakeholder -> {
            notification.setOwner(stakeholder);
            Notification notificationSaved = notificationServices.saveNotification(notification);
            simpMessagingTemplate.convertAndSend("/queue/" + stakeholder + "/documentary-load-completed",
                    notificationSaved);
        });
    }
}
